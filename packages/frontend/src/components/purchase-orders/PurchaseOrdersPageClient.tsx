'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, RefreshCw, BarChart2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

import { DataTable } from '@/components/purchase-orders/data-table';
import { createPurchaseOrderColumns } from '@/components/purchase-orders/columns';
import { PurchaseOrderQuickView } from '@/components/purchase-orders/purchase-order-quick-view';
import { SendToSupplierDialog } from '@/components/purchase-orders/send-to-supplier-dialog';
import { PurchaseOrder, PurchaseOrderQueryParams, PurchaseOrderWithRelations } from '@/types/purchase-order';
import {
  usePurchaseOrders,
  usePurchaseOrder,
  usePurchaseOrdersInvalidate,
  useCancelPurchaseOrder,
  useSendPurchaseOrder,
  useDeletePurchaseOrder,
  useExportPurchaseOrders,
  useDownloadPurchaseOrderPdf,
} from '@/hooks/usePurchaseOrders';
import { useSuppliers } from '@/hooks/useSuppliers';
import { toast } from 'sonner';
import { DEFAULT_PURCHASE_ORDER_PAGINATION } from '@/lib/constants/purchase-order';
import { PurchaseOrderStatsCards } from './purchase-order-stats-cards';

interface PurchaseOrdersPageClientProps {
  initialQuery: PurchaseOrderQueryParams;
}

export function PurchaseOrdersPageClient({
  initialQuery,
}: PurchaseOrdersPageClientProps) {
  const router = useRouter();

  // Query state - this will trigger the TanStack Query
  const [query, setQuery] = useState<PurchaseOrderQueryParams>(initialQuery);
  const [selectedPurchaseOrderId, setSelectedPurchaseOrderId] = useState<string | null>(null);
  const [showQuickView, setShowQuickView] = useState(false);
  const [showSendToSupplierDialog, setShowSendToSupplierDialog] = useState(false);
  const [showStatsCards, setShowStatsCards] = useState(false);
  // Track the navigation context for send to supplier modal
  const [sendToSupplierContext, setSendToSupplierContext] = useState<'table' | 'quickview' | null>(null);

  // Use TanStack Query for data fetching
  const { data, isLoading, refetch } = usePurchaseOrders(query);
  const { data: selectedPurchaseOrder, isLoading: isLoadingSelectedPurchaseOrder } = usePurchaseOrder(selectedPurchaseOrderId || '');

  // Use invalidation hook for stats refresh
  const { invalidateAll } = usePurchaseOrdersInvalidate();

  // Use mutation hooks for purchase order operations
  const cancelPurchaseOrderMutation = useCancelPurchaseOrder();
  const sendPurchaseOrderMutation = useSendPurchaseOrder();
  const deletePurchaseOrderMutation = useDeletePurchaseOrder();
  const exportPurchaseOrdersMutation = useExportPurchaseOrders();
  const downloadPurchaseOrderPdfMutation = useDownloadPurchaseOrderPdf();

  // Get suppliers for filter options
  const { data: suppliersResponse } = useSuppliers({
    page: 1,
    limit: 100,
    sortBy: 'name',
    sortOrder: 'asc'
  });

  const supplierOptions = suppliersResponse?.data?.map(supplier => ({
    value: supplier.id,
    label: `${supplier.name} (${supplier.code})`,
  })) || [];

  // Update query parameters - similar to inventory pattern
  const handleQueryChange = useCallback((newQuery: PurchaseOrderQueryParams) => {
    setQuery(newQuery);

    // Update URL without navigation to preserve scroll position
    const params = new URLSearchParams();
    Object.entries(newQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, String(value));
      }
    });

    const newUrl = `/dashboard/purchase-orders?${params.toString()}`;
    const currentUrl = window.location.pathname + window.location.search;

    if (newUrl !== currentUrl) {
      window.history.replaceState(null, '', newUrl);
    }
  }, []);

  // Filter handlers - similar to inventory pattern
  const handleFilterChange = useCallback((key: keyof PurchaseOrderQueryParams, value: any) => {
    const newFilters = {
      ...query,
      [key]: value,
      page: 1, // Reset to first page when filtering
    };

    handleQueryChange(newFilters);
  }, [query, handleQueryChange]);

  const handleBatchFilterChange = useCallback((updates: Partial<PurchaseOrderQueryParams>) => {
    const newFilters = {
      ...query,
      ...updates,
      page: 1, // Reset to first page when filtering
    };

    handleQueryChange(newFilters);
  }, [query, handleQueryChange]);

  const clearFilters = useCallback(() => {
    const clearedFilters = {
      page: 1,
      limit: DEFAULT_PURCHASE_ORDER_PAGINATION.limit,
      sortBy: DEFAULT_PURCHASE_ORDER_PAGINATION.sortBy,
      sortOrder: DEFAULT_PURCHASE_ORDER_PAGINATION.sortOrder,
      search: undefined, // Clear search field
    };
    handleQueryChange(clearedFilters);
  }, [handleQueryChange]);

  const handleViewPurchaseOrder = useCallback((purchaseOrder: PurchaseOrder) => {
    setSelectedPurchaseOrderId(purchaseOrder.id);
    setShowQuickView(true);
  }, []);

  const handleEditPurchaseOrder = useCallback((purchaseOrder: PurchaseOrder) => {
    router.push(`/dashboard/purchase-orders/${purchaseOrder.id}/edit`);
  }, [router]);

  const handleCreatePurchaseOrder = useCallback(() => {
    router.push('/dashboard/purchase-orders/new');
  }, [router]);

  const handleCancelPurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder, reason: string) => {
    try {
      await cancelPurchaseOrderMutation.mutateAsync({
        id: purchaseOrder.id,
        reason
      });
      invalidateAll();
      if (selectedPurchaseOrderId === purchaseOrder.id) {
        // Refresh the selected purchase order
        refetch();
      }
    } catch (error) {
      console.error('Failed to cancel purchase order:', error);
    }
  }, [cancelPurchaseOrderMutation, invalidateAll, selectedPurchaseOrderId, refetch]);

  // Handler for "Kirim ke supplier" from table action dropdown
  const handleSendPurchaseOrderFromTable = useCallback((purchaseOrder: PurchaseOrder) => {
    setSelectedPurchaseOrderId(purchaseOrder.id);
    setSendToSupplierContext('table');
    setShowSendToSupplierDialog(true);
    // Ensure quick view is closed if it was open
    setShowQuickView(false);
  }, []);

  // Handler for "Kirim ke supplier" from quick view dialog
  const handleSendPurchaseOrderFromQuickView = useCallback((purchaseOrder: PurchaseOrderWithRelations) => {
    setSelectedPurchaseOrderId(purchaseOrder.id);
    setSendToSupplierContext('quickview');
    setShowQuickView(false); // Close quick view first
    setShowSendToSupplierDialog(true);
  }, []);

  const handleDeletePurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder) => {
    try {
      await deletePurchaseOrderMutation.mutateAsync(purchaseOrder.id);
      invalidateAll();
      if (selectedPurchaseOrderId === purchaseOrder.id) {
        setShowQuickView(false);
        setSelectedPurchaseOrderId(null);
      }
    } catch (error) {
      console.error('Failed to delete purchase order:', error);
    }
  }, [deletePurchaseOrderMutation, invalidateAll, selectedPurchaseOrderId]);

  const handlePrintPurchaseOrder = useCallback(async (purchaseOrder: PurchaseOrder) => {
    try {
      await downloadPurchaseOrderPdfMutation.mutateAsync(purchaseOrder.id);
    } catch (error) {
      console.error('Failed to print purchase order:', error);
    }
  }, [downloadPurchaseOrderPdfMutation]);

  const handleDownloadPdf = useCallback(async (purchaseOrder: PurchaseOrderWithRelations) => {
    await downloadPurchaseOrderPdfMutation.mutateAsync(purchaseOrder.id);
  }, [downloadPurchaseOrderPdfMutation]);

  const handleActualSendToSupplier = useCallback(async (purchaseOrder: PurchaseOrderWithRelations) => {
    try {
      await sendPurchaseOrderMutation.mutateAsync(purchaseOrder.id);
      invalidateAll();
      if (selectedPurchaseOrderId === purchaseOrder.id) {
        refetch();
      }
    } catch (error) {
      console.error('Failed to send purchase order:', error);
    }
  }, [sendPurchaseOrderMutation, invalidateAll, selectedPurchaseOrderId, refetch]);

  const handleExport = useCallback(async () => {
    try {
      await exportPurchaseOrdersMutation.mutateAsync({
        ...query,
        format: 'xlsx',
      });
    } catch (error) {
      console.error('Failed to export purchase orders:', error);
    }
  }, [exportPurchaseOrdersMutation, query]);

  const handleRefresh = useCallback(() => {
    refetch();
    invalidateAll();
  }, [refetch, invalidateAll]);

  const handleViewDetails = useCallback((purchaseOrder: PurchaseOrder) => {
    handleGoToPage(`/dashboard/purchase-orders/${purchaseOrder.id}`);
  }, [router]);

  const handleGoToPage = useCallback((path: string) => {
    router.push(path);
  }, [router]);

  // Payment handlers
  const handleViewPayments = useCallback((purchaseOrder: PurchaseOrderWithRelations) => {
    // Navigate to purchase order detail page with payment tab
    router.push(`/dashboard/purchase-orders/${purchaseOrder.id}?tab=payments`);
  }, [router]);

  const handleAddPayment = useCallback((purchaseOrder: PurchaseOrderWithRelations) => {
    // Navigate to purchase order detail page with add payment action
    router.push(`/dashboard/purchase-orders/${purchaseOrder.id}?tab=payments&action=add`);
  }, [router]);

  const handleViewPaymentSummary = useCallback((purchaseOrder: PurchaseOrderWithRelations) => {
    // Navigate to purchase order detail page with payment summary
    router.push(`/dashboard/purchase-orders/${purchaseOrder.id}?tab=payment-summary`);
  }, [router]);

  // Create columns with action handlers
  const columns = createPurchaseOrderColumns({
    onView: handleViewPurchaseOrder,
    onEdit: handleEditPurchaseOrder,
    onDelete: handleDeletePurchaseOrder,
    onCancel: (po) => handleCancelPurchaseOrder(po, 'Dibatalkan dari tabel'),
    onSend: handleSendPurchaseOrderFromTable, // Use table-specific handler
    onPrint: handlePrintPurchaseOrder,
    onViewPayments: handleViewPayments,
    onAddPayment: handleAddPayment,
    onViewPaymentSummary: handleViewPaymentSummary,
    goToPage: handleGoToPage,
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-background py-3 -mt-4 md:-mt-6 lg:-mx-6 -mx-4 lg:px-6 shadow px-4 sticky top-0 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Purchase Orders</h1>
          <p className="text-muted-foreground">
            Kelola purchase order dan pemesanan ke supplier
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleExport}
            disabled={exportPurchaseOrdersMutation.isPending}
          >
            <Download className="h-4 w-4 mr-2" />
            {exportPurchaseOrdersMutation.isPending ? 'Mengekspor...' : 'Ekspor'}
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowStatsCards(!showStatsCards)}
            disabled={isLoading} 
          >
            <BarChart2 className="h-4 w-4 mr-2" />
            { showStatsCards ? 'Sembunyikan Statistik' : `Lihat Statistik`}
          </Button>
          <Button onClick={handleCreatePurchaseOrder}>
            <Plus className="h-4 w-4 mr-2" />
            Buat Purchase Order
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {showStatsCards && <PurchaseOrderStatsCards />}

      {/* Data Table */}
      <div className="w-full min-w-0 max-w-full">
        <DataTable
          columns={columns}
          data={data?.data || []}
          meta={data?.meta || {
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false
          }}
          query={query}
          onQueryChange={handleQueryChange}
          loading={isLoading}
          searchPlaceholder="Cari purchase order..."
          onRowClick={handleViewPurchaseOrder}
          filters={query}
          onFilterChange={handleFilterChange}
          onBatchFilterChange={handleBatchFilterChange}
          onClearFilters={clearFilters}
          filterOptions={{
            suppliers: supplierOptions,
          }}
        />
      </div>

      {/* Quick View Modal */}
      <PurchaseOrderQuickView
        purchaseOrder={selectedPurchaseOrder || null}
        open={showQuickView}
        onOpenChange={(open) => {
          setShowQuickView(open);
          if (!open) {
            setSelectedPurchaseOrderId(null);
          }
        }}
        onEdit={handleEditPurchaseOrder}
        onCancel={handleCancelPurchaseOrder}
        onSend={handleSendPurchaseOrderFromQuickView} // Use quick view-specific handler
        onPrint={handlePrintPurchaseOrder}
        onViewDetails={handleViewDetails}
      />

      {/* Send to Supplier Dialog */}
      <SendToSupplierDialog
        open={showSendToSupplierDialog}
        onOpenChange={(open) => {
          setShowSendToSupplierDialog(open);
          if (!open) {
            // Handle different navigation flows based on context
            if (sendToSupplierContext === 'quickview') {
              // Return to quick view if came from quick view
              setShowQuickView(true);
            } else {
              // Clear selection if came from table
              setSelectedPurchaseOrderId(null);
            }
            setSendToSupplierContext(null);
          }
        }}
        purchaseOrder={selectedPurchaseOrder || null}
        onDownloadPdf={handleDownloadPdf}
        onSendToSupplier={handleActualSendToSupplier}
        isDownloading={downloadPurchaseOrderPdfMutation.isPending}
        isSending={sendPurchaseOrderMutation.isPending}
        isLoadingPurchaseOrder={isLoadingSelectedPurchaseOrder}
      />
    </div>
  );
}
