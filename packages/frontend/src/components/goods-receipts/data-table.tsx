'use client';

import * as React from 'react';
import {
  ColumnDef,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Settings2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { GoodsReceiptStatus, GoodsReceiptQueryParams, GoodsReceiptListResponse } from '@/types/goods-receipt';
import { SupplierSelector } from '@/components/ui/supplier-selector';
import { useDebounce, DEBOUNCE_DELAYS } from '@/lib/utils/debounce';
import { GOODS_RECEIPT_PAGE_SIZES } from '@/lib/constants/goods-receipt';

// Constants for "All" filter options
const ALL_STATUS = '__ALL_STATUS__';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  meta: GoodsReceiptListResponse['meta'];
  searchPlaceholder?: string;
  onRowClick?: (row: TData) => void;
  onQueryChange?: (query: GoodsReceiptQueryParams) => void;
  loading?: boolean;
  query: GoodsReceiptQueryParams;
  filters: GoodsReceiptQueryParams;
  onFilterChange: (key: keyof GoodsReceiptQueryParams, value: any) => void;
  onBatchFilterChange?: (updates: Partial<GoodsReceiptQueryParams>) => void;
  onClearFilters: () => void;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  meta,
  searchPlaceholder = 'Cari penerimaan barang...',
  onRowClick,
  onQueryChange,
  loading = false,
  query,
  filters,
  onFilterChange,
  onBatchFilterChange,
  onClearFilters,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [searchValue, setSearchValue] = React.useState(query.search || '');

  // Debounced search using centralized utility
  const debouncedSearch = useDebounce(searchValue, DEBOUNCE_DELAYS.SEARCH);

  // Sync search value with query changes (when query comes from URL or external source)
  React.useEffect(() => {
    if (query.search !== searchValue) {
      setSearchValue(query.search || '');
    }
  }, [query.search]);

  // Use refs to avoid stale closures
  const queryRef = React.useRef(query);
  const onQueryChangeRef = React.useRef(onQueryChange);

  React.useEffect(() => {
    queryRef.current = query;
    onQueryChangeRef.current = onQueryChange;
  });

  // Trigger query change when debounced search changes
  React.useEffect(() => {
    if (onQueryChangeRef.current && debouncedSearch !== queryRef.current.search) {
      onQueryChangeRef.current({
        ...queryRef.current,
        search: debouncedSearch || undefined,
        page: 1, // Reset to first page when searching
      });
    }
  }, [debouncedSearch]);

  // Handle sorting
  React.useEffect(() => {
    if (sorting.length > 0) {
      const sort = sorting[0];
      if (onQueryChange) {
        onQueryChange({
          ...query,
          sortBy: sort.id,
          sortOrder: sort.desc ? 'desc' : 'asc',
          page: 1, // Reset to first page when sorting
        });
      }
    }
  }, [sorting]);

  // Handle pagination - use onQueryChange like inventory table
  const handlePageChange = (newPage: number) => {
    onQueryChange?.({
      ...query,
      page: newPage,
    });
  };

  const handleLimitChange = (newLimit: string) => {
    onQueryChange?.({
      ...query,
      limit: parseInt(newLimit),
      page: 1, // Reset to first page
    });
  };

  // Count active filters - use memoization to prevent accumulation issues
  const activeFiltersCount = React.useMemo(() => {
    let count = 0;

    // Basic filters
    if (filters.status) count++;
    if (filters.supplierId) count++;
    if (filters.purchaseOrderId) count++;
    if (filters.search) count++;

    // Date filters
    if (filters.receiptDateFrom || filters.receiptDateTo) count++;
    if (filters.deliveryDateFrom || filters.deliveryDateTo) count++;

    return count;
  }, [filters]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <div className="w-full min-w-0 max-w-full space-y-3 sm:space-y-4">
      {/* Enhanced Mobile-Friendly Toolbar */}
      <div className="flex flex-col gap-2 sm:gap-3">

        {/* Single Row on Desktop: Search + Filters + Column Visibility */}
        <div className="flex flex-col gap-2 lg:flex-row lg:items-center lg:gap-4">

          {/* Search Input */}
          <div className="relative flex-1 lg:max-w-sm">
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(event) => setSearchValue(event.target.value)}
              className="pr-10 h-9 w-full"
              disabled={loading}
            />
            {loading && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          {/* Filter Controls - responsive layout */}
          <div className="flex flex-col gap-2 sm:flex-row sm:flex-wrap sm:items-center sm:gap-2 lg:gap-3">

            {/* Status Filter */}
            <div className="w-full sm:w-auto">
              <Select
                value={filters.status || ALL_STATUS}
                onValueChange={(value) => onFilterChange('status', value === ALL_STATUS ? undefined : value as GoodsReceiptStatus)}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[140px]">
                  <SelectValue placeholder="Status" />
                  {loading && (
                    <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
                      <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ALL_STATUS}>Semua Status</SelectItem>
                  <SelectItem value={GoodsReceiptStatus.PENDING}>Menunggu</SelectItem>
                  <SelectItem value={GoodsReceiptStatus.REJECTED}>Ditolak</SelectItem>
                  <SelectItem value={GoodsReceiptStatus.COMPLETED}>Selesai</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Supplier Filter */}
            <div className="w-full sm:w-auto">
              <SupplierSelector
                value={filters.supplierId || 'none'}
                onValueChange={(value) => onFilterChange('supplierId', value === 'none' ? undefined : value)}
                placeholder="Semua Supplier"
                allowNone={true}
                noneLabel="Semua Supplier"
                className="h-9 w-full sm:w-[200px]"
                disabled={loading}
              />
            </div>

            {/* Clear Filters */}
            {activeFiltersCount > 0 && (
              <div className="w-full sm:w-auto">
                <Button
                  variant="ghost"
                  onClick={onClearFilters}
                  className="h-9 w-full sm:w-auto px-3"
                  disabled={loading}
                >
                  <X className="mr-2 h-4 w-4" />
                  Reset ({activeFiltersCount})
                </Button>
              </div>
            )}
          </div>

          {/* Column Visibility */}
          <div className="w-full sm:w-auto lg:ml-auto">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 w-full sm:w-auto px-3">
                  <Settings2 className="h-4 w-4 mr-1" />
                  Kolom
                  <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id === 'receiptNumber' && 'Nomor Penerimaan'}
                        {column.id === 'supplier' && 'Supplier'}
                        {column.id === 'purchaseOrder' && 'Purchase Order'}
                        {column.id === 'receiptDate' && 'Tanggal Terima'}
                        {column.id === 'status' && 'Status'}
                        {column.id === 'totalAmount' && 'Total'}
                        {column.id === 'createdAt' && 'Dibuat'}
                        {column.id === 'actions' && 'Aksi'}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Table Container with horizontal scroll and sticky actions */}
      <div className="w-full min-w-0 max-w-full overflow-hidden rounded-md border">
        {/* Enhanced Scroll indicators */}
        <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-3 text-sm text-blue-700 dark:text-blue-300 border-b border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-center gap-2">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Tabel dapat digulir horizontal</span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        <div className="w-full overflow-x-auto" id="table-scroll-container">
          <Table className="min-w-[1400px] border-t">
            <TableHeader className="bg-muted">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    const isActionsColumn = header.id === 'actions';
                    const isTotalAmountColumn = header.id === 'totalAmount';
                    return (
                      <TableHead
                        key={header.id}
                        className={`px-2 lg:px-4 ${isTotalAmountColumn ? 'text-right' : ''
                          } ${isActionsColumn
                          ? 'sticky right-0 bg-muted border-l shadow-lg z-10'
                          : ''
                          }`}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading state - maintain table structure with skeleton rows
                Array.from({ length: query.limit || 10 }).map((_, index) => (
                  <TableRow key={`loading-${index}`}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={colIndex} className="px-2 lg:px-4">
                        <div className="h-4 bg-muted animate-pulse rounded"></div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onRowClick?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => {
                      const isActionsColumn = cell.column.id === 'actions';
                      return (
                        <TableCell
                          key={cell.id}
                          onClick={isActionsColumn ? (e) => e.stopPropagation() : undefined}
                          className={`px-2 lg:px-4 ${isActionsColumn
                            ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                            : ''
                            }`}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Tidak ada data penerimaan barang.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="hidden items-center gap-2 flex-1 lg:flex">
          <Label htmlFor="rows-per-page" className="text-sm font-medium">
            Baris per halaman
          </Label>
          <Select
            value={String(query.limit || 10)}
            onValueChange={handleLimitChange}
            disabled={loading}
          >
            <SelectTrigger size="sm" className="w-20" id="rows-per-page">
              <SelectValue />
            </SelectTrigger>
            <SelectContent side="top">
              {GOODS_RECEIPT_PAGE_SIZES.map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-full items-center gap-8 lg:w-fit">
          <div className="flex w-fit items-center justify-center text-sm font-medium">
            Menampilkan {((query.page || 1) - 1) * (query.limit || 10) + 1} - {Math.min((query.page || 1) * (query.limit || 10), meta.total)} dari {meta.total} data
          </div>
          <div className="ml-auto flex items-center gap-2 lg:ml-0">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => handlePageChange(1)}
              disabled={loading || !meta.hasPreviousPage}
            >
              <span className="sr-only">Ke halaman pertama</span>
              <ChevronsLeft />
            </Button>
            <Button
              variant="outline"
              className="size-8"
              size="icon"
              onClick={() => handlePageChange((query.page || 1) - 1)}
              disabled={loading || !meta.hasPreviousPage}
            >
              <span className="sr-only">Ke halaman sebelumnya</span>
              <ChevronLeft />
            </Button>
            <Button
              variant="outline"
              className="size-8"
              size="icon"
              onClick={() => handlePageChange((query.page || 1) + 1)}
              disabled={loading || !meta.hasNextPage}
            >
              <span className="sr-only">Ke halaman selanjutnya</span>
              <ChevronRight />
            </Button>
            <Button
              variant="outline"
              className="hidden size-8 lg:flex"
              size="icon"
              onClick={() => handlePageChange(meta.totalPages)}
              disabled={loading || !meta.hasNextPage}
            >
              <span className="sr-only">Ke halaman terakhir</span>
              <ChevronsRight />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
