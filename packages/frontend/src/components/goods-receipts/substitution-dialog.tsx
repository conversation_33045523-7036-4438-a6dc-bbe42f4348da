"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { ProductSelector } from "@/components/ui/product-selector";
import { Package, ArrowRight } from "lucide-react";
import { toast } from "sonner";
import { useProduct } from "@/hooks/useProducts";

const substitutionReasons = [
  { value: "unavailable", label: "Produk tidak tersedia" },
  { value: "out_of_stock", label: "Stok habis" },
  { value: "expired", label: "Produk kadaluarsa" },
  { value: "damaged", label: "Produk rusak" },
  { value: "price_change", label: "Perubahan harga" },
  { value: "packaging_different", label: "Kemasan berbeda" },
  { value: "supplier_recommendation", label: "Rekomendasi supplier" },
  { value: "other", label: "Lainnya" },
];

const substitutionSchema = z.object({
  substitutionProductId: z.string().min(1, "Produk substitusi harus dipilih"),
  substitutionReason: z.string().min(1, "Alasan substitusi harus dipilih"),
  substitutionNotes: z.string().min(5, "Catatan minimal 5 karakter"),
});

type SubstitutionFormData = z.infer<typeof substitutionSchema>;

interface SubstitutionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  originalProductId: string;
  originalProductName?: string;
  itemIndex: number;
  onSubstitutionConfirmed: (
    itemIndex: number,
    substitutionData: {
      productId: string;
      substitutionReason: string;
      substitutionNotes: string;
      originalProductId: string;
    }
  ) => void;
}

export function SubstitutionDialog({
  open,
  onOpenChange,
  originalProductId,
  originalProductName,
  itemIndex,
  onSubstitutionConfirmed,
}: SubstitutionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch original product data to get the actual product name
  const { data: originalProduct, isLoading: isLoadingOriginalProduct } = useProduct(originalProductId);

  const form = useForm<SubstitutionFormData>({
    resolver: zodResolver(substitutionSchema),
    defaultValues: {
      substitutionProductId: "",
      substitutionReason: "",
      substitutionNotes: "",
    },
  });

  const onSubmit = async (data: SubstitutionFormData) => {
    setIsSubmitting(true);
    try {
      const substitutionData = {
        productId: data.substitutionProductId,
        substitutionReason: data.substitutionReason,
        substitutionNotes: data.substitutionNotes,
        originalProductId,
      };

      onSubstitutionConfirmed(itemIndex, substitutionData);

      toast.success("Produk berhasil disubstitusi");
      handleClose();
    } catch (error) {
      toast.error("Gagal melakukan substitusi produk");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  const selectedReason = form.watch("substitutionReason");
  const reasonLabel = substitutionReasons.find(r => r.value === selectedReason)?.label;

  // Get the display name for the original product
  const displayOriginalProductName = isLoadingOriginalProduct
    ? "Memuat..."
    : originalProduct?.name || originalProductName || "Produk Tidak Diketahui";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Substitusi Produk
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Original vs New Product */}
          <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
            <div className="flex-1">
              <p className="text-sm text-muted-foreground">Produk Asli:</p>
              <p className="font-medium">{displayOriginalProductName}</p>
              {originalProduct?.code && (
                <p className="text-xs text-muted-foreground">Kode: {originalProduct.code}</p>
              )}
            </div>
            <ArrowRight className="h-5 w-5 text-muted-foreground" />
            <div className="flex-1">
              <p className="text-sm text-muted-foreground">Produk Substitusi:</p>
              <p className="font-medium">
                {form.watch("substitutionProductId") ? "Produk dipilih" : "Belum dipilih"}
              </p>
              {reasonLabel && (
                <p className="text-xs text-orange-600">Alasan: {reasonLabel}</p>
              )}
            </div>
          </div>

          {/* Substitution Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Product Selection */}
              <FormField
                control={form.control}
                name="substitutionProductId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Produk Substitusi *</FormLabel>
                    <FormControl>
                      <ProductSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Pilih produk substitusi..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Substitution Reason */}
              <FormField
                control={form.control}
                name="substitutionReason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alasan Substitusi *</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih alasan substitusi..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {substitutionReasons.map((reason) => (
                          <SelectItem key={reason.value} value={reason.value}>
                            {reason.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Substitution Notes */}
              <FormField
                control={form.control}
                name="substitutionNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Catatan Substitusi *</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Jelaskan detail substitusi (contoh: harga sama, kemasan beda, supplier rekomendasikan, dll...)"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose}>
            Batal
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? "Memproses..." : "Konfirmasi Substitusi"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
